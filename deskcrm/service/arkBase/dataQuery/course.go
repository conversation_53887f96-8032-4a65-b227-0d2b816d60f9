package dataQuery

import (
	"deskcrm/api/allocate"
	"deskcrm/api/dal"
	"deskcrm/api/lpcleads"
	"deskcrm/api/moat"
	"deskcrm/components"
	"deskcrm/consts"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func (s *Singleton) GetCourseExpiredTime(ctx *gin.Context, courseId int64) (expiredTime int64, err error) {
	expiredMap, err := lpcleads.NewClient().GetBatchExpireTimeByCourseID(ctx, []int64{courseId})
	if err != nil {
		zlog.Warnf(ctx, "GetCourseExpiredTime failed: %s", err.Error())
		return 0, nil
	}

	if _, ok := expiredMap[courseId]; !ok {
		return 0, nil
	}

	return expiredMap[courseId].ExpireTime, nil
}

/**
 * 从lpcleads服务获取例子信息
 * @return array|mixed|null
 */
func (s *Singleton) GetLeadsInfos(ctx *gin.Context, leadsIds []int64) (leadsInfoMap map[int64]*allocate.LeadsInfo, err error) {
	leadsInfoMap = map[int64]*allocate.LeadsInfo{}
	leadsInfoMap, err = allocate.NewClient().GetLeadsByIds(ctx, leadsIds)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseExpiredTime failed: %s", err.Error())
		return leadsInfoMap, nil
	}
	return leadsInfoMap, nil
}

// 获取失效例子列表
func (s *Singleton) GetExpiredLeadsMap(ctx *gin.Context, courseId int64, leadsIds []int64) (expiredLeadsMap map[int64]*allocate.LeadsInfo, err error) {
	expiredLeadsMap = map[int64]*allocate.LeadsInfo{}
	expiredTime, _ := s.GetCourseExpiredTime(ctx, courseId)
	if expiredTime < time.Now().Unix() {
		return expiredLeadsMap, nil
	}
	lpcLeadsInfoMap, _ := s.GetLeadsInfos(ctx, leadsIds)
	for _, leadsInfo := range lpcLeadsInfoMap {
		if leadsInfo.Status == allocate.LeadsInfoStatusInvalid {
			expiredLeadsMap[leadsInfo.LeadsId] = leadsInfo
		}
	}
	return
}

// 根据课程id获取skuId
func (s *Singleton) GetSkuIdByCourseId(ctx *gin.Context, courseId int64) (skuId int64, err error) {
	conds := map[string]interface{}{
		"op": "and",
		"aggs": []map[string]interface{}{
			{
				"type": 0,
				"conds": map[string]interface{}{
					"key":   "thirdId",
					"value": []int64{courseId},
					"exps":  "in",
				},
			},
			{
				"type": 0,
				"conds": map[string]interface{}{
					"key":   "skuMode",
					"value": components.SkuCourseMode,
					"exps":  "eq",
				},
			},
		},
	}

	skuList, err := moat.NewClient().GetSkuListByCondition(ctx, conds, nil, 0, 1, []string{"skuId", "thirdId"}, nil)
	if err != nil {
		return
	}

	for _, item := range skuList {
		if item.SkuId > 0 {
			return item.SkuId, nil
		}
	}

	return
}
