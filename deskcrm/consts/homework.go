package consts

// 作业相关常量定义

// 作业等级映射，对应PHP中的Api_Das::$homeworkLevelMap
var HomeworkLevelMap = map[int64]string{
	1: "S",
	2: "A",
	3: "B",
	4: "C",
	5: "D",
}

// 作业状态常量
const (
	// 满分标识，对应PHP中的HOMEWORK_FULL_MARKS_CODE
	HomeworkFullMarksCode = "S"

	// 作业开启状态
	HomeworkOpen = 1

	// 作业绑定类型，对应PHP中的BindTypeHomework
	BindTypeHomework = 7
)

// 考试绑定类型常量，对应PHP中的Api_Exam::BIND_TYPE_*
const (
	BindTypePracticeInClass = 1 // 课中练习（互动题）
	BindTypeFinal           = 2 // 期末考试（废弃）
	BindTypePreTest         = 3 // 报前测试
	BindTypePostTest        = 4 // 报后测试
	BindTypePreview         = 5 // 课前预习
	BindTypeReview          = 6 // 课后复习（废弃）
	// BindTypeHomework        = 7  // 课后作业（已在上面定义）
	BindTypeMission            = 8  // 任务（废弃）
	BindTypeStage              = 9  // 阶段性测试
	BindTypeTestInClass        = 10 // 堂堂测
	BindTypePrimaryMathPreview = 11 // 小学-数学-同步练习
	BindTypeMockExam           = 12 // 模考
	BindTypePosttestMore       = 13 // 初高中预习测试
	BindTypeOralQuestion       = 14 // 口述题
)

// 作业状态枚举
const (
	HomeworkStatusNotAssigned  = 0 // 未布置
	HomeworkStatusNotSubmitted = 1 // 未提交
	HomeworkStatusNotGraded    = 2 // 提交未批改
	HomeworkStatusGraded       = 3 // 已批改
)

// 作业订正状态枚举
const (
	HomeworkRecorrectNotSubmitted = 0 // 未提交
	HomeworkRecorrectSubmitted    = 1 // 已提交
	HomeworkRecorrectWaitGrade    = 2 // 待批改
	HomeworkRecorrectGraded       = 3 // 已批改
	HomeworkRecorrectWaitRegrade  = 4 // 待重批
	HomeworkRecorrectWaitResubmit = 5 // 待重提
)

// 试卷订正状态枚举，对应PHP中的EXAM_CORRECT_STATUS_*常量
const (
	ExamCorrectStatusUnassigned    = 1 // 未布置
	ExamCorrectStatusTbSubmitted   = 2 // 待提交
	ExamCorrectStatusTbCorrected   = 3 // 待批改
	ExamCorrectStatusTbRecorrected = 4 // 待重批
	ExamCorrectStatusTbResubmitted = 5 // 待重提
	ExamCorrectStatusCorrected     = 6 // 已订正
	ExamCorrectStatusAudit         = 7 // 待审核
)

// 试卷订正状态映射，对应PHP中的$examCorrectStatusMap
var ExamCorrectStatusMap = map[int]string{
	ExamCorrectStatusUnassigned:    "未布置",
	ExamCorrectStatusTbSubmitted:   "待提交",
	ExamCorrectStatusTbCorrected:   "待批改",
	ExamCorrectStatusTbRecorrected: "待重提", // 修正：4对应"待重提"
	ExamCorrectStatusTbResubmitted: "待重批", // 修正：5对应"待重批"
	ExamCorrectStatusCorrected:     "已订正",
	ExamCorrectStatusAudit:         "待审核",
}

// 不展示评级的试卷批改状态集合，对应PHP中的EXAM_CORRECT_STATUS_WAIT_SHOW_MAP
var ExamCorrectStatusWaitShowMap = map[int]bool{
	ExamCorrectStatusUnassigned:  true, // 1 - 未布置
	ExamCorrectStatusTbSubmitted: true, // 2 - 待提交
	ExamCorrectStatusTbCorrected: true, // 3 - 待批改
}

// ILab相关常量
const (
	// 初二物理课程标识
	ILabGradeId   = 3 // 初二
	ILabSubjectId = 4 // 物理

	// ILab版本
	ILabVersion2 = 2
)

// 口述题状态常量
const (
	OralQuStatusUnknown     = -1 // 未知状态
	OralQuStatusUnsubmit    = 0  // 未提交
	OralQuStatusSubmit      = 1  // 已提交
	OralQuStatusTbCorrected = 2  // 待批改
)

// 口述题状态映射
var OralQuStatusMap = map[int]string{
	OralQuStatusUnknown:     "-",
	OralQuStatusUnsubmit:    "未提交",
	OralQuStatusSubmit:      "已完成",
	OralQuStatusTbCorrected: "待批改",
}

// 颜色常量
const (
	ColorGreen  = "green"
	ColorOrange = "orange"
	ColorGray   = "gray"
)

// 通用状态文本
const (
	StatusNotSubmitted = "未提交"
	StatusNotGraded    = "未批改"
	StatusNoLevel      = "暂无等级"
	StatusNotAssigned  = "未布置"
	StatusWaitGrade    = "待批改"
	StatusWaitAudit    = "待审核"
	StatusWaitRegrade  = "待重批"
	StatusWaitResubmit = "待重提"
	StatusGraded       = "已批改"
	StatusDash         = "-"
)

// 互动题相关常量
const (
	// 互动题格式模板，对应PHP中的LESSON_EXERCISE_DETAIL
	LessonExerciseDetail = "%d|%d|%d"

	// 灰度测试key，对应PHP中的INACT_TOTAL_NUM_TANS_GRAY_KEY
	InactTotalNumTransGrayKey = "inact_total_num_trans_gray"
)

// 互动题状态码
const (
	ExerciseCodeNotParticipated = 0 // 未参与
	ExerciseCodeAllCorrect      = 1 // 全答全对
	ExerciseCodeNotAllCorrect   = 2 // 未全答全对
)

// 订正相关常量，对应PHP中的Hkzb_Const_Correction
const (
	HomeworkTypeOld           = 1 // 老批改系统，对应PHP中的HOMEWORK_TYPE_OLD
	HomeworkTypeNewCorrection = 2 // 新批改系统，订正，对应PHP中的HOMEWORK_TYPE_NEW_CORRECTION
	HomeworkTypeNewLike       = 3 // 新批改系统，只作答查看解析视频，不过相似题，对应PHP中的HOMEWORK_TYPE_NEW_LIKE
	HomeworkTypeNewLikeExam   = 4 // 新批改系统，作答查看解析视频，做相似题，对应PHP中的HOMEWORK_TYPE_NEW_LIKE_EXAM
)

// 订正开始时间常量，对应PHP中的CORRECTION_START_TIME
const (
	CorrectionStartTime = 1582214400 // 2020.01.01测试用，上线前改成正式的
)

// 黑名单课程ID，对应PHP中的Hkzb_Const_Correction::$BlackList
var CorrectionBlackList = []int64{
	570254, 570256, // 2020暑二高二英语物理
}

// 加油站课程ID，对应PHP中的Hkzb_Const_Correction::$JyzCourseIdArr
var CorrectionJyzCourseIdArr = []int64{
	363648, 363718, 363724, 363730, 363736, 363748,
	424922, 428413, 428419, 427322, 433019, 433026,
	433465, 433471,
}
